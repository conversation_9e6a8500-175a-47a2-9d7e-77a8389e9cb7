# Comments are provided throughout this file to help you get started.
# If you need more help, visit the Docker compose reference guide at
# https://docs.docker.com/compose/compose-file/

# Here the instructions define your application as a service called "server".
# This service is built from the Dockerfile in the current directory.
# You can add other services your application may depend on here, such as a
# database or a cache. For examples, see the Awesome Compose repository:
# https://github.com/docker/awesome-compose
services:

  # Dev-Umgebung mit Hot Reload
    dev:
        build:
          context: .
        command: npm run devD --prefix ./toolboxv2
        volumes:
          - ./toolboxv2/.data:/app/toolboxv2/.data
          - ./toolboxv2/data:/app/toolboxv2/data
          - ./toolboxv2/.config:/app/toolboxv2/.config
          - ./toolboxv2/.info:/app/toolboxv2/.info
        env_file:
            - .env
        environment:
          - ENVIRONMENT=development
        ports:
            - 5000:5000
            - 6587:6587
        # develop:
        #     watch:
        #         - action: sync
        #           path: ./toolboxv2/utils
        #           target: /app/toolboxv2/utils
        #         - action: sync
        #           path: ./toolboxv2/web
        #           target: /app/toolboxv2/web
        #           ignore:
        #               - node_modules/
        #         - action: sync
        #           path: ./toolboxv2/mods
        #           target: /app/toolboxv2/mods
        #         - action: rebuild
        #           path: ./toolboxv2/package.json
        #           target: /app/toolboxv2/package.json
        #         - action: rebuild
        #           path: ./toolboxv2/flows
        #           target: /app/toolboxv2/flows

      # Live-Umgebung mit Zero Downtime
    live:
        build:
            context: .
        command: npm run liveD --prefix ./toolboxv2
        volumes:
            - ./toolboxv2/.data:/app/toolboxv2/.data
            - ./toolboxv2/data:/app/toolboxv2/data
            - ./toolboxv2/.config:/app/toolboxv2/.config
            - ./toolboxv2/.info:/app/toolboxv2/.info
        env_file:
            - .env
        environment:
            - ENVIRONMENT=production
        ports:
            - 5000:5000
            - 6587:6587
            - 8080:8080
            - 8000:8000
        deploy:
            update_config:
                parallelism: 1
                delay: 10s
                order: start-first
                failure_action: rollback
            restart_policy:
                condition: any
        restart: always

      # Testumgebung
    test:
        build:
          context: .
        command: tb -n test --test
        ports:
            - 5000:5000
            - 6587:6587
        env_file:
            - .env
        environment:
          - ENVIRONMENT=test

    redis:
        image: redis:7-alpine
        container_name: toolboxv2-redis
        ports:
          - "6379:6379"
        volumes:
          - redis-data:/data
        restart: unless-stopped

volumes:
  redis-data:
# The commented out section below is an example of how to define a PostgreSQL
# database that your application can use. `depends_on` tells Docker Compose to
# start the database before your application. The `db-data` volume persists the
# database data between container restarts. The `db-password` secret is used
# to set the database password. You must create `db/password.txt` and add
# a password of your choosing to it before running `docker compose up`.
#     depends_on:
#       db:
#         condition: service_healthy
#   db:
#     image: postgres
#     restart: always
#     user: postgres
#     secrets:
#       - db-password
#     volumes:
#       - db-data:/var/lib/postgresql/data
#     environment:
#       - POSTGRES_DB=example
#       - POSTGRES_PASSWORD_FILE=/run/secrets/db-password
#     expose:
#       - 5432
#     healthcheck:
#       test: [ "CMD", "pg_isready" ]
#       interval: 10s
#       timeout: 5s
#       retries: 5
# volumes:
#   db-data:
# secrets:
#   db-password:
#     file: db/password.txt

