Quality Check Report - 2025-05-13-162735
Commit: 3de024041ee86e43f6d3c3945b78cde49831b14b
========================================

[Ruff] Data not found in temp storage.

[Safety] Exit Code: 64
[Safety] Output:
+==============================================================================+

                               /$$$$$$            /$$
                              /$$__  $$          | $$
           /$$$$$$$  /$$$$$$ | $$  \__//$$$$$$  /$$$$$$   /$$   /$$
          /$$_____/ |____  $$| $$$$   /$$__  $$|_  $$_/  | $$  | $$
         |  $$$$$$   /$$$$$$$| $$_/  | $$$$$$$$  | $$    | $$  | $$
          \____  $$ /$$__  $$| $$    | $$_____/  | $$ /$$| $$  | $$
          /$$$$$$$/|  $$$$$$$| $$    |  $$$$$$$  |  $$$$/|  $$$$$$$
         |_______/  \_______/|__/     \_______/   \___/   \____  $$
                                                          /$$  | $$
                                                         |  $$$$$$/
  by safetycli.com                                        \______/

+==============================================================================+

 [1mREPORT[0m 

  Safety [1mv3.2.4[0m is scanning for [1mVulnerabilities[0m[1m...[0m
[1m  Scanning dependencies[0m in your [1menvironment:[0m

  -> c:\users\<USER>\workspace\toolboxv2\.venv\lib\site-
  packages\setuptools\_vendor
  -> C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-
  none\Lib
  ->
  C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-none
  -> c:\users\<USER>\workspace\toolboxv2\.venv\lib\site-packages
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\Pythonwin
  -> C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-
  none\DLLs
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\win32\lib
  -> C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-
  none\python312.zip
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\win32
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages
  -> __editable__.isaa-0.1.4.finder.__path_hook__
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Scripts\safety.exe
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-
  packages\setuptools\_vendor

  Using [1mopen-source vulnerability database[0m
[1m  Found and scanned 340 packages[0m
  Timestamp [1m2025-05-13 16:26:03[0m
[1m  1[0m[1m vulnerability reported[0m
[1m  0[0m[1m vulnerabilities ignored[0m

+==============================================================================+
 [1mVULNERABILITIES REPORTED[0m 
+==============================================================================+

[31m-> Vulnerability found in langchain-experimental version 0.3.4[0m
[1m   Vulnerability ID: [0m73280
[1m   Affected spec: [0m>=0.1.17
[1m   ADVISORY: [0mA vulnerability exists in langchain_experimental
   affected versions where the LLMSymbolicMathChain was introduced because it
   passes untrusted input directly to sympy.sympify, which uses eval()
   internally. This flaw allows attackers to execute arbitrary code via
   crafted mathematical expressions.
[1m   CVE-2024-46946[0m
[1m   For more information about this vulnerability, visit
   [0mhttps://data.safetycli.com/v/73280/97c[0m
   To ignore this vulnerability, use PyUp vulnerability id 73280 in safety�s
   ignore command-line argument or add the ignore to your safety policy file.


+==============================================================================+
   [32m[1mREMEDIATIONS[0m

  1 vulnerability was reported in 1 package. For detailed remediation & fix 
  recommendations, upgrade to a commercial license. 

+==============================================================================+

 Scan was completed. 1 vulnerability was reported. 

+==============================================================================+[0m

[Versions] Exit Code: 0
[Versions] Output:
Starting ToolBox as main from : [1m[36mC:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2[0m[0m
Logger in Default
================================
[36mSystem$main-DESKTOP-CI57V1L:[0m Infos:
  Name     -> DESKTOP-CI57V1L
  ID       -> main-DESKTOP-CI57V1L
  Version  -> 0.1.21

LOADING ALL MODS FROM FOLDER : mods
Opened : CodeVerification
Opened : FileWidget
Opened : DashProvider
Opened : setup
Opened : MinimalHtml

**************************************************************************
Opened : welcome
Opened : <module 'toolboxv2.mods.TruthSeeker' from 'C:\\Users\\<USER>\\Workspace\\ToolBoxV2\\toolboxv2\\mods\\TruthSeeker\\__init__.py'>
Opened : SocketManager
Opened : WhatsAppTb
Opened : ProcessManager
Opened : TestWidget
Opened : email_waiting_list
Opened : email_waiting_list
SchedulerManager try loading from file
SchedulerManager Successfully loaded
STARTING SchedulerManager
Opened : WebSocketManager
module toolboxv2.mods.audio is not valid
Overriding function delete from DB
module toolboxv2.mods.audio is not valid
Opened : SchedulerManager
Opened : cli_functions
Opened : FastApi
Overriding function Version from DB
Opened : DB
module toolboxv2.mods.audio is not valid
Opened : DoNext
Opened : WidgetsProvider.BoardWidget
module toolboxv2.mods.audio is not valid
Overriding function Version from CloudM
Function talk On start result: Talke Offline
Opened : talk
Overriding function show_version from CloudM
Overriding function get_mod_snapshot from CloudM
Opened : CloudM
[Taichi] version 1.7.3, llvm 15.0.1, commit 5ec301be, win, python 3.12.9
[95misaa[0m: Start app.isaa
Spinner Manager not in the min Thread no signal possible

[KOpened : isaa

[KOpened 27 modules in 7.89s

------------------ Version ------------------

[1m[36m[3mRE[0m[0m[0m[3mSimple[0mToolBox:  0.1.21  

         CodeVerification          :  0.0.1   
              DoNext               :  0.1.21  
           DashProvider            :  0.0.1   
            FileWidget             :  1.0.0   
               setup               :  0.0.3   
               talk                :  0.0.1   
              welcome              :  0.1.21  
            MinimalHtml            :  0.0.2   
            TruthSeeker            : unknown  
           SocketManager           :  0.1.9   
            WhatsAppTb             : unknown  
          ProcessManager           :  0.0.1   
            TestWidget             :  0.0.1   
           EventManager            :  0.0.3   
        email_waiting_list         :  0.1.21  
         SchedulerManager          :  0.0.2   
         WebSocketManager          :  0.0.3   
                DB                 :  0.0.3   
           cli_functions           :  0.0.1   
              FastApi              :  0.2.2   
        CloudM.AuthManager         :  0.1.21  
         CloudM.UI.widget          :  0.0.1   
       CloudM.UserInstances        :  0.0.2   
          WidgetsProvider          :  0.0.1   
              CloudM               :  0.0.3   
    WidgetsProvider.BoardWidget    :  0.0.1   
               isaa                :  0.1.5   



[K
Building State data:   0%|          | 0/6 [00:00<?, ?chunk/s]
                                                             

Building State data:   0%|          | 0/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  17%|#6        | 1/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  33%|###3      | 2/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  50%|#####     | 3/6 [00:00<?, ?chunk/s]working on utils files
working on api files
working on app files
working on mods files
Overriding function get_widget from FileWidget
Overriding function upload from FileWidget
Overriding function download from FileWidget
Overriding function files from FileWidget
Overriding function Version from MinimalHtml
Function On Exit result: saved 0 jobs in C:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2\.data\main-DESKTOP-CI57V1L/jobs.compact
Overriding function add_group from MinimalHtml
[1m[3m- end -[0m[0m

