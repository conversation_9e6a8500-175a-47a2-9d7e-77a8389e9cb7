#!/usr/bin/env python3
"""
Demo build script for ToolBox Browser Extension
Creates a sample build to demonstrate functionality
"""

import sys
import os
import json
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from installer import ToolBoxExtensionInstaller
    
    def demo_build():
        print("🚀 ToolBox Browser Extension - Demo Build")
        print("=" * 60)
        
        # Create installer instance
        installer = ToolBoxExtensionInstaller()
        installer.version = "1.0.0-demo"
        
        print(f"📁 Working directory: {installer.script_dir}")
        print(f"🔢 Version: {installer.version}")
        
        # Detect browsers
        print("\n🔍 Detecting installed browsers...")
        detected_browsers = installer.detect_browsers()
        
        for browser, installed in detected_browsers.items():
            status = "✅ Installed" if installed else "❌ Not found"
            print(f"  {browser}: {status}")
        
        # Create a demo build for Chrome (most common)
        print("\n🔨 Creating demo build for Chrome...")
        
        try:
            # Build Chrome extension
            build_path = installer.build_extension('chrome')
            print(f"✅ Chrome extension built at: {build_path}")
            
            # List generated files
            print("\n📄 Generated files:")
            for file_path in build_path.rglob('*'):
                if file_path.is_file():
                    size = file_path.stat().st_size
                    print(f"  📄 {file_path.name} ({size} bytes)")
            
            # Show manifest content
            manifest_path = build_path / "manifest.json"
            if manifest_path.exists():
                print(f"\n📋 Manifest content preview:")
                with open(manifest_path, 'r') as f:
                    manifest = json.load(f)
                    print(f"  Name: {manifest.get('name')}")
                    print(f"  Version: {manifest.get('version')}")
                    print(f"  Manifest Version: {manifest.get('manifest_version')}")
                    print(f"  Permissions: {len(manifest.get('permissions', []))}")
            
            # Package the extension
            print(f"\n📦 Packaging extension...")
            package_path = installer.package_extension('chrome', build_path)
            print(f"✅ Extension packaged at: {package_path}")
            
            # Show installation instructions
            print(f"\n📋 Installation Instructions:")
            print(f"1. Open Chrome and go to chrome://extensions/")
            print(f"2. Enable 'Developer mode' (top right)")
            print(f"3. Click 'Load unpacked'")
            print(f"4. Select folder: {build_path}")
            print(f"")
            print(f"Or install the packaged version:")
            print(f"1. Drag and drop {package_path.name} onto chrome://extensions/")
            
            # Generate store assets
            print(f"\n🎨 Generating store assets...")
            installer.generate_store_assets()
            print(f"✅ Store assets generated at: {installer.dist_dir}/store_assets/")
            
            print(f"\n🎉 Demo build complete!")
            print(f"📁 Build directory: {installer.build_dir}")
            print(f"📦 Distribution directory: {installer.dist_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ Build failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    if __name__ == "__main__":
        success = demo_build()
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"❌ Failed to import installer: {e}")
    print("Make sure installer.py is in the same directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Demo failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
