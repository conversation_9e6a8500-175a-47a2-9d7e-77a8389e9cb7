{"manifest_version": 3, "name": "ToolBox Browser Extension", "version": "2.0.0", "description": "Advanced ToolBox integration with Voice I/O, Smart Search, and ISAA-2 AI capabilities", "permissions": ["storage", "activeTab", "scripting", "notifications", "contextMenus", "webNavigation", "tabs", "background", "clipboardWrite", "clipboardRead", "offscreen", "webRequest"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["core/utils.js", "core/isaa-plugin.js", "core/voice-engine.js", "core/search-engine.js", "core/gesture-detector.js", "core/ui-manager.js", "password-manager.js", "content.js"], "css": ["styles.css"], "run_at": "document_start"}], "action": {"default_popup": "popup.html", "default_title": "ToolBox Extension - Voice & AI Search", "default_icon": {"16": "icons/tb16.png", "32": "icons/tb32.png", "48": "icons/tb48.png", "128": "icons/tb128.png"}}, "web_accessible_resources": [{"resources": ["inject.js", "tb-core.js", "plugins/*", "assets/*", "voice/*", "search/*"], "matches": ["<all_urls>"]}], "commands": {"toggle-toolbox": {"suggested_key": {"default": "Alt+T"}, "description": "Toggle ToolBox Panel"}, "voice-command": {"suggested_key": {"default": "Ctrl+Shift+V"}, "description": "Activate Voice Commands"}, "password-manager": {"suggested_key": {"default": "Ctrl+Shift+P"}, "description": "Open Password Manager"}, "auto-fill": {"suggested_key": {"default": "Ctrl+Shift+F"}, "description": "Auto-fill Login Form"}}}