# ToolBox Browser Extension

A comprehensive AI-powered browser extension that integrates ISAA (Intelligent System Agent Architecture) with advanced password management and web automation capabilities.

## 🚀 Features

### 🧠 ISAA AI Assistant
- **Natural Language Processing**: Understands user intent for web interactions
- **Context Awareness**: Analyzes current page content, forms, and elements
- **Multi-Modal Interaction**: Text input, voice commands, and selected text processing
- **Real-time Responses**: Live search results with intelligent suggestions

### 🔒 Advanced Password Management
- **Secure Password Generation**: 16-character passwords with customizable complexity
- **Intelligent Auto-fill**: Automatic form detection and credential filling
- **2FA/TOTP Support**: Complete two-factor authentication with visual countdown
- **Password Manager**: Full CRUD operations with search and filtering

### 🎤 Voice Recognition System
- **Speech-to-Text**: Convert voice input to text for ISAA
- **Selected Text Input**: Use highlighted text as voice input
- **Visual Feedback**: Animated voice indicator with status
- **Multi-language Support**: Configurable language detection

### 🔍 Smart Search & Navigation
- **Live Search Features**: 300ms debounced search with instant feedback
- **ISAA-Powered**: AI-enhanced search with context understanding
- **Visual Loading**: Professional spinner and progress indicators
- **Fallback System**: Graceful degradation to local search

## 📦 Installation

### Prerequisites
- Python 3.8 or higher
- ToolBox server running on localhost:8080
- Supported browsers: Chrome, Firefox, Edge, Safari

### Quick Start

1. **Clone or navigate to the extension directory:**
   ```bash
   cd toolboxv2/tb_browser_extension
   ```

2. **Run the installer:**
   ```bash
   # Build for all detected browsers
   python installer.py --build-all

   # Build and install in development mode
   python installer.py --build-all --install-dev

   # Build for specific browser only
   python installer.py --browser chrome --install-dev
   ```

3. **Follow the browser-specific installation instructions** that will be displayed.

### Advanced Installation Options

```bash
# Package existing builds without rebuilding
python installer.py --package-only

# Create mobile packages and instructions only
python installer.py --mobile-only

# Build with custom version
python installer.py --build-all --version 1.2.0

# Build without mobile support
python installer.py --build-all --no-include-mobile
```

## 🛠️ Development Installation

### Chrome
1. Open Chrome and go to `chrome://extensions/`
2. Enable 'Developer mode' (top right)
3. Click 'Load unpacked'
4. Select the build folder: `build/chrome/`

### Firefox
1. Open Firefox and go to `about:debugging`
2. Click 'This Firefox'
3. Click 'Load Temporary Add-on'
4. Select `manifest.json` from: `build/firefox/`

### Edge
1. Open Edge and go to `edge://extensions/`
2. Enable 'Developer mode' (left sidebar)
3. Click 'Load unpacked'
4. Select the build folder: `build/edge/`

### Safari
1. Enable Safari Developer menu (Safari > Preferences > Advanced)
2. Go to Develop > Show Extension Builder
3. Click '+' and add extension folder
4. Select the build folder: `build/safari/`

## 🎯 Usage

### Keyboard Shortcuts
- **Alt+T**: Toggle ToolBox panel
- **Escape**: Close modals and overlays

### Quick Actions
- **🤖 ISAA Chat**: Open AI assistant for natural language interaction
- **🔑 Generate**: Create secure passwords instantly
- **📝 Auto-fill**: Fill forms with saved credentials
- **🔒 Passwords**: Access password manager

### Voice Commands
1. Click the microphone button in the ISAA input
2. Speak your command naturally
3. The extension will process and execute the request

### Example Commands
- "Fill this login form"
- "Generate a strong password"
- "Click the submit button"
- "Extract all links from this page"
- "Help me understand this form"

## 🏗️ Architecture

### File Structure
```
tb_browser_extension/
├── installer.py              # Main installer script
├── styles.css               # Global styling with glass morphism
├── core/
│   ├── ui-manager.js        # Main UI management
│   ├── isaa-plugin.js       # ISAA AI integration
│   ├── utils.js             # Utility functions
│   └── voice-engine.js      # Voice recognition system
├── build/                   # Generated build files
├── dist/                    # Distribution packages
└── README.md               # This file
```

### Generated Files (per browser)
- `manifest.json` - Extension manifest (browser-specific)
- `background.js` - Service worker/background script
- `content.js` - Content script for page interaction
- `popup.html/js` - Extension popup interface
- `icons/` - Extension icons (16, 32, 48, 128px)

## 🔧 Configuration

### API Endpoints
The extension communicates with the ToolBox server at `http://localhost:8080`:

- `/api/isaa/mini_task_completion` - ISAA AI processing
- `/api/isaa/format_class` - Structured response formatting
- `/api/PasswordManager/generate_password` - Password generation
- `/api/PasswordManager/get_password_for_autofill` - Auto-fill data
- `/api/PasswordManager/list_passwords` - Password list
- `/api/PasswordManager/generate_totp_code` - TOTP generation

### Settings
Extension settings are stored locally and include:
- Theme preference (dark/light)
- Panel position
- Voice language
- Password generation defaults
- Auto-hide behavior

## 📱 Mobile Support

### Android
- Chrome Mobile: Limited extension support
- Firefox Mobile: Use Firefox Nightly with extension support enabled

### iOS
- Safari Mobile: Requires conversion to iOS app extension
- See `dist/mobile/ios_instructions.txt` for detailed steps

## 🎨 Design System

### Color Palette
- Primary: `#6c8ee8` (ToolBox Blue)
- Secondary: `#1a8cff` (Accent Blue)
- Background: `#181823` (Dark)
- Text: `#E9F8F9` (Light)

### Glass Morphism
- Backdrop blur: 20px
- Background: `rgba(20, 20, 30, 0.95)`
- Border: `rgba(255, 255, 255, 0.1)`
- Shadow: `0 20px 40px rgba(0, 0, 0, 0.3)`

## 🔒 Security & Privacy

### Data Protection
- **Local Processing**: All data processing happens locally
- **No External Servers**: No data sent to external servers without consent
- **Encrypted Storage**: Password data is encrypted locally
- **Minimal Permissions**: Only required permissions are requested

### Permissions
- `activeTab`: Required for page interaction and form filling
- `storage`: Used for local settings and preferences
- `contextMenus`: Provides right-click menu options
- `scripting`: Enables content script injection

## 🚀 Distribution

### Browser Stores
Generated packages are ready for submission to:
- Chrome Web Store (`.zip`)
- Firefox Add-ons (`.xpi`)
- Edge Add-ons (`.zip`)
- Safari App Store (requires additional conversion)

### Store Assets
The installer generates store-ready assets:
- Descriptions for each browser store
- Privacy policy
- Screenshots and promotional materials

## 🐛 Troubleshooting

### Common Issues

1. **Extension not loading**
   - Ensure ToolBox server is running on localhost:8080
   - Check browser console for errors
   - Verify manifest.json is valid

2. **API calls failing**
   - Confirm ToolBox server is accessible
   - Check network connectivity
   - Verify API endpoints are responding

3. **Voice recognition not working**
   - Ensure microphone permissions are granted
   - Check if browser supports Web Speech API
   - Verify HTTPS context (required for some browsers)

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('tb-debug', 'true');
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test across browsers
5. Submit a pull request

## 📄 License

This project is part of the ToolBox ecosystem. See the main ToolBox license for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review browser console for error messages
- Ensure ToolBox server is running and accessible
- Verify all prerequisites are met

---

**Built with ❤️ by the ToolBox Team**
