#!/usr/bin/env python3
"""
Test script for ToolBox Browser Extension Installer
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from installer import ToolBoxExtensionInstaller
    
    def test_installer():
        print("🧪 Testing ToolBox Browser Extension Installer")
        print("=" * 50)
        
        # Create installer instance
        installer = ToolBoxExtensionInstaller()
        
        # Test browser detection
        print("\n🔍 Testing browser detection...")
        detected_browsers = installer.detect_browsers()
        print(f"Detected browsers: {detected_browsers}")
        
        # Test manifest creation
        print("\n📄 Testing manifest creation...")
        for browser in ['chrome', 'firefox', 'edge', 'safari']:
            try:
                manifest = installer.create_manifest(browser)
                print(f"✅ {browser}: manifest created (version {manifest.get('manifest_version')})")
            except Exception as e:
                print(f"❌ {browser}: {e}")
        
        # Test script creation
        print("\n📜 Testing script creation...")
        try:
            background_script = installer.create_background_script('chrome')
            print(f"✅ Background script created ({len(background_script)} chars)")
        except Exception as e:
            print(f"❌ Background script: {e}")
        
        try:
            content_script = installer.create_content_script()
            print(f"✅ Content script created ({len(content_script)} chars)")
        except Exception as e:
            print(f"❌ Content script: {e}")
        
        try:
            popup_html = installer.create_popup_html()
            print(f"✅ Popup HTML created ({len(popup_html)} chars)")
        except Exception as e:
            print(f"❌ Popup HTML: {e}")
        
        try:
            popup_js = installer.create_popup_script()
            print(f"✅ Popup script created ({len(popup_js)} chars)")
        except Exception as e:
            print(f"❌ Popup script: {e}")
        
        print("\n✅ All tests passed! Installer is ready to use.")
        print("\nTo build extensions, run:")
        print("  python installer.py --build-all")
        print("  python installer.py --build-all --install-dev")
        
        return True
        
    if __name__ == "__main__":
        test_installer()
        
except ImportError as e:
    print(f"❌ Failed to import installer: {e}")
    print("Make sure installer.py is in the same directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
