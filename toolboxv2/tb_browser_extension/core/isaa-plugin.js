// ToolBox Browser Extension - ISAA AI Integration Plugin
// Handles all ISAA AI assistant functionality

class ISAAPlugin {
    constructor() {
        this.apiBase = 'http://localhost:8080';
        this.isConnected = false;
        this.chatHistory = [];
        this.currentContext = null;
        this.isProcessing = false;

        this.init();
    }

    async init() {
        // Check connection to ISAA service
        await this.checkConnection();

        // Initialize context extraction
        this.updatePageContext();

        console.log('ISAA Plugin initialized');
    }

    async checkConnection() {
        try {
            const response = await this.makeAPICall('/api/isaa/version', 'GET');
            this.isConnected = true;
            console.log('✅ Connected to ISAA service');
        } catch (error) {
            this.isConnected = false;
            console.warn('⚠️ ISAA service not available:', error.message);
        }
    }

    updatePageContext() {
        this.currentContext = {
            url: window.location.href,
            title: document.title,
            timestamp: Date.now(),
            forms: this.extractForms(),
            links: this.extractLinks(),
            content: this.extractPageContent(),
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
    }

    extractForms() {
        return Array.from(document.forms).map(form => ({
            action: form.action || window.location.href,
            method: form.method || 'GET',
            id: form.id,
            className: form.className,
            fields: Array.from(form.elements).map(element => ({
                name: element.name,
                type: element.type,
                id: element.id,
                placeholder: element.placeholder,
                required: element.required,
                value: element.type === 'password' ? '[HIDDEN]' : element.value?.substring(0, 100)
            }))
        }));
    }

    extractLinks() {
        return Array.from(document.links)
            .slice(0, 20) // Limit to first 20 links
            .map(link => ({
                href: link.href,
                text: link.textContent.trim().substring(0, 100),
                title: link.title
            }))
            .filter(link => link.text.length > 0);
    }

    extractPageContent() {
        // Extract meaningful text content
        const content = {
            headings: [],
            paragraphs: [],
            lists: []
        };

        // Extract headings
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        content.headings = Array.from(headings)
            .slice(0, 10)
            .map(h => ({
                level: parseInt(h.tagName.charAt(1)),
                text: h.textContent.trim().substring(0, 200)
            }));

        // Extract paragraphs
        const paragraphs = document.querySelectorAll('p');
        content.paragraphs = Array.from(paragraphs)
            .slice(0, 5)
            .map(p => p.textContent.trim().substring(0, 300))
            .filter(text => text.length > 20);

        // Extract lists
        const lists = document.querySelectorAll('ul, ol');
        content.lists = Array.from(lists)
            .slice(0, 3)
            .map(list => ({
                type: list.tagName.toLowerCase(),
                items: Array.from(list.querySelectorAll('li'))
                    .slice(0, 5)
                    .map(li => li.textContent.trim().substring(0, 100))
            }));

        return content;
    }

    async processMessage(message, mode = 'chat') {
        if (this.isProcessing) {
            throw new Error('Already processing a request');
        }

        this.isProcessing = true;

        try {
            // Update context before processing
            this.updatePageContext();

            // Determine intent
            const intent = this.analyzeIntent(message);

            // Prepare request data
            const requestData = {
                mini_task: message,
                user_task: `Web interaction on ${this.currentContext.title}`,
                mode: mode,
                agent_name: 'web-assistant',
                task_from: 'browser_extension',
                context: {
                    ...this.currentContext,
                    intent: intent,
                    chat_history: this.chatHistory.slice(-5) // Last 5 messages for context
                }
            };

            // Make API call
            const response = await this.makeAPICall('/api/isaa/mini_task_completion', 'POST', requestData);

            // Add to chat history
            this.chatHistory.push({
                role: 'user',
                content: message,
                timestamp: Date.now()
            });

            this.chatHistory.push({
                role: 'assistant',
                content: response.response,
                timestamp: Date.now(),
                actions: response.actions,
                data: response.data
            });

            return response;

        } finally {
            this.isProcessing = false;
        }
    }

    analyzeIntent(message) {
        const lowerMessage = message.toLowerCase();

        // Form filling intent
        if (lowerMessage.includes('fill') || lowerMessage.includes('form') ||
            lowerMessage.includes('login') || lowerMessage.includes('sign in')) {
            return { type: 'form-filling', confidence: 0.8 };
        }

        // Navigation intent
        if (lowerMessage.includes('click') || lowerMessage.includes('go to') ||
            lowerMessage.includes('navigate') || lowerMessage.includes('open')) {
            return { type: 'navigation', confidence: 0.8 };
        }

        // Data extraction intent
        if (lowerMessage.includes('extract') || lowerMessage.includes('get') ||
            lowerMessage.includes('find') || lowerMessage.includes('scrape')) {
            return { type: 'scraping', confidence: 0.7 };
        }

        // Password/security intent
        if (lowerMessage.includes('password') || lowerMessage.includes('login') ||
            lowerMessage.includes('2fa') || lowerMessage.includes('totp')) {
            return { type: 'security', confidence: 0.9 };
        }

        // Default to chat
        return { type: 'chat', confidence: 0.6 };
    }

    async executeAction(action, data = {}) {
        switch (action) {
            case 'fill-form':
                return this.fillForm(data);
            case 'click-element':
                return this.clickElement(data);
            case 'extract-data':
                return this.extractData(data);
            case 'generate-password':
                return this.generatePassword(data);
            case 'auto-fill-credentials':
                return this.autoFillCredentials(data);
            default:
                throw new Error(`Unknown action: ${action}`);
        }
    }

    async fillForm(data) {
        const { formSelector, fields } = data;
        const form = formSelector ? document.querySelector(formSelector) : document.forms[0];

        if (!form) {
            throw new Error('Form not found');
        }

        let filledCount = 0;

        for (const [fieldName, value] of Object.entries(fields)) {
            const field = form.querySelector(`[name="${fieldName}"], #${fieldName}`);
            if (field && field.type !== 'password') {
                field.value = value;
                field.dispatchEvent(new Event('input', { bubbles: true }));
                filledCount++;
            }
        }

        return { success: true, filledFields: filledCount };
    }

    async clickElement(data) {
        const { selector, text } = data;
        let element;

        if (selector) {
            element = document.querySelector(selector);
        } else if (text) {
            // Find element by text content
            const elements = Array.from(document.querySelectorAll('button, a, [onclick]'));
            element = elements.find(el =>
                el.textContent.toLowerCase().includes(text.toLowerCase())
            );
        }

        if (!element) {
            throw new Error('Element not found');
        }

        element.click();
        return { success: true, element: element.tagName };
    }

    async extractData(data) {
        const { selector, attribute = 'textContent' } = data;
        const elements = document.querySelectorAll(selector);

        const extracted = Array.from(elements).map(el => {
            if (attribute === 'textContent') {
                return el.textContent.trim();
            } else {
                return el.getAttribute(attribute);
            }
        });

        return { success: true, data: extracted };
    }

    async generatePassword(data = {}) {
        const requestData = {
            length: data.length || 16,
            include_symbols: data.include_symbols !== false,
            include_numbers: data.include_numbers !== false,
            include_uppercase: data.include_uppercase !== false,
            include_lowercase: data.include_lowercase !== false,
            exclude_ambiguous: data.exclude_ambiguous !== false
        };

        const response = await this.makeAPICall('/api/PasswordManager/generate_password', 'POST', requestData);

        if (response.success) {
            // Copy to clipboard
            await navigator.clipboard.writeText(response.data.password);
            return { success: true, password: response.data.password, copied: true };
        }

        throw new Error('Password generation failed');
    }

    async autoFillCredentials(data = {}) {
        const url = data.url || window.location.href;
        const response = await this.makeAPICall('/api/PasswordManager/get_password_for_autofill', 'POST', { url });

        if (response.success && response.data.entry) {
            const entry = response.data.entry;

            // Find form fields
            const usernameField = document.querySelector(
                'input[type="email"], input[name*="user"], input[name*="email"], input[autocomplete*="username"]'
            );
            const passwordField = document.querySelector('input[type="password"]');

            let filled = { username: false, password: false };

            if (usernameField && entry.username) {
                usernameField.value = entry.username;
                usernameField.dispatchEvent(new Event('input', { bubbles: true }));
                filled.username = true;
            }

            if (passwordField && entry.password) {
                passwordField.value = entry.password;
                passwordField.dispatchEvent(new Event('input', { bubbles: true }));
                filled.password = true;
            }

            // Show TOTP if available
            if (response.data.totp_code) {
                window.UIManager?.createTOTPDisplay(
                    response.data.totp_code,
                    entry.title || 'Account',
                    response.data.time_remaining || 30
                );
            }

            return { success: true, filled, totp: !!response.data.totp_code };
        }

        throw new Error('No credentials found for this site');
    }

    async makeAPICall(endpoint, method = 'POST', data = null) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                type: 'API_REQUEST',
                data: {
                    endpoint: endpoint,
                    method: method,
                    body: data
                }
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response.error || 'API call failed'));
                }
            });
        });
    }

    clearChatHistory() {
        this.chatHistory = [];
    }

    getChatHistory() {
        return [...this.chatHistory];
    }

    getConnectionStatus() {
        return this.isConnected;
    }
}

// Initialize ISAA Plugin
window.ISAAPlugin = new ISAAPlugin();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ISAAPlugin;
}
