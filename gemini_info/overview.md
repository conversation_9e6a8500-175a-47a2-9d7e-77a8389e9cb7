# ToolBoxV2 Overview

ToolBoxV2 is a modular framework for building and deploying tools and applications.

## Core Components

*   **Python Backend:** The core logic and tool definitions reside in the `toolboxv2` Python package.
*   **Rust Server (Actix):** An Actix-based web server provides the backend for web and desktop applications. The source is in `~oolboxv2/src-core`.
*   **Frontend (Tauri/tbjs):** A Tauri-based application provides the cross-platform desktop UI, with the web UI components located in `~oolboxv2/tbjs`.

## Key Features

*   **Modular Architecture:** Tools and applications are built as independent modules.
*   **Multiple Deployment Options:** Can be run as a CLI tool, a web server, or a desktop application.
*   **Extensible:** New tools and functionalities can be added by creating new Python modules.
