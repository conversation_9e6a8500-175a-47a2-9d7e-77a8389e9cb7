# Frontend (Tauri/tbjs)

## Structure

The frontend code is split across several directories:

*   **`~oolboxv2/tbjs`:** This directory contains the source code for the `tbjs` library, which appears to be a core component of the frontend.
*   **`~oolboxv2/web`:** This directory contains the web application assets, including HTML, CSS, and JavaScript.
*   **`~oolboxv2/simple-core`:** This directory likely contains the Tauri application source code.
*   **`~oolboxv2/package.json`:** This file defines the frontend dependencies and build scripts.

## Dependencies

The frontend uses the following key dependencies:

*   **`webpack`:** A module bundler for JavaScript applications.
*   **`tauri`:** A framework for building lightweight, cross-platform desktop applications with a web frontend.
*   **`tbjs`:** A custom library that likely provides the core functionality for the user interface.

## Purpose

The frontend provides the user interface for the ToolBoxV2 application. It can be run as a desktop application using Tauri or as a web application in a browser. The frontend communicates with the Rust server to access the application's core functionality.
