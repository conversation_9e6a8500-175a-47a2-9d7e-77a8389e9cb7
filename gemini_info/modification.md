# Modifying ToolBoxV2

This document explains how to modify and extend the ToolBoxV2 application.

## Adding New Python Modules

To add a new Python module, create a new subdirectory in the `toolboxv2/mods` directory. The subdirectory should contain an `__init__.py` file that defines the module's entry point. The entry point should be a class that inherits from `toolboxv2.utils.system.main_tool.MainTool`.

## Modifying the Frontend

The frontend can be modified by editing the files in the `~oolboxv2/tbjs` and `~oolboxv2/web` directories. The `tbjs` directory contains the source code for the `tbjs` library, which provides the core UI components. The `web` directory contains the web application assets, including HTML, CSS, and JavaScript.

After making changes to the frontend, you will need to rebuild the application using the following command:

```bash
npm run build
```

## Modifying the Rust Server

The Rust server can be modified by editing the files in the `~oolboxv2/src-core` directory. After making changes to the server, you will need to rebuild it using the following command:

```bash
cargo build --release
```

## Communicating with ISAA Toolbox MCP Tools

Communication with the ISAA Toolbox MCP tools is likely handled through the Python core. You will need to create a new Python module that implements the necessary logic for interacting with the MCP tools. This may involve using a library such as `requests` to make HTTP requests to the MCP tools' API.
