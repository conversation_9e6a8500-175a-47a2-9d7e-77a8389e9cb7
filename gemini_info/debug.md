# Debugging ToolBoxV2

This document outlines the procedures for debugging the different components of ToolBoxV2.

## Python Core

To debug the Python core, you can use the `--debug` flag when running the application. This will enable hot-reloading, which automatically restarts the application when changes are made to the source code.

```bash
tb --debug
```

You can also use a traditional Python debugger, such as `pdb` or the debugger integrated into your IDE. Attach the debugger to the running `tb` process to set breakpoints and inspect the application's state.

## Rust Server

To debug the Rust server, you can run it in development mode using the following command:

```bash
tb api debug
```

This will start the server with debugging symbols and enable more verbose logging. You can then use a Rust debugger, such as `gdb` or `lldb`, to attach to the running process.

## Frontend

To debug the frontend, you can use the developer tools built into your web browser or the Tauri application. To open the developer tools in the Tauri application, right-click on the application and select "Inspect Element".

This will open a developer console that allows you to inspect the HTML, CSS, and JavaScript of the application, as well as set breakpoints and debug the code.
