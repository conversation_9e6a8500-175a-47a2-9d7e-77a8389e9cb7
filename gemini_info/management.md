# Managing ToolBoxV2

This document describes how to manage a ToolBoxV2 instance, including starting, stopping, and configuring the application.

## Running the Application

ToolBoxV2 can be run in several different modes:

*   **CLI:** The default mode, which provides a command-line interface for interacting with the application.
*   **GUI:** The graphical user interface, which can be launched with the `tb gui` command.
*   **API Server:** The Rust server can be started with the `tb api start` command.

## Service Management

On Linux and Windows, ToolBoxV2 can be run as a service, which allows it to start automatically on boot and restart if it crashes.

*   **Linux:** Use the `tb --sm` command to manage the systemd service.
*   **Windows:** Use the `tb --sm` command to manage the Windows service.

## Configuration

The main configuration file for ToolBoxV2 is `config.toml`. This file contains settings for the Python core, the Rust server, and the frontend.

## Local Instance Management

*   **Starting and Stopping:** Use the `tb` command to start the application and `Ctrl+C` to stop it. For background processes, use `tb -bg` to start and `tb --kill` to stop.
*   **Updating:** Use `tb -u [module_name]` to update a specific module or `tb -u main` to update the core application.
