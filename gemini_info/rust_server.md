# Rust Server (Actix)

## Structure

The Rust server code is located in the `~oolboxv2/src-core` directory. The main components are:

*   **`src/main.rs`:** The main entry point for the server application.
*   **`src/lib.rs`:** The library entry point, likely used for the PyO3 integration.
*   **`Cargo.toml`:** The package manifest, which defines the project's dependencies and metadata.

## Dependencies

The server uses the following key dependencies:

*   **`actix-web`:** A powerful, pragmatic, and extremely fast web framework for Rust.
*   **`pyo3`:** For Rust bindings to the Python interpreter, enabling interoperability between Rust and Python.
*   **`tokio`:** An asynchronous runtime for Rust.
*   **`serde`:** A framework for serializing and deserializing Rust data structures efficiently and generically.

## Purpose

The Rust server provides a high-performance backend for the web and desktop applications. It serves the frontend assets and provides an API for the frontend to interact with the Python core.
