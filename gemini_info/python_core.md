# Python Core

## Structure

The Python core of ToolBoxV2 is located in the `toolboxv2` directory. It contains the following key subdirectories:

*   `~oolboxv2/utils`: Core utilities for system interaction, file handling, logging, and type definitions.
*   `~oolboxv2/mods`: Directory for installable modules.
*   `~oolboxv2/flows`: Contains the logic for different application flows (e.g., 'cli', 'docker').

## Entry Points

*   **`~oolboxv2/__main__.py`:** The main entry point for the command-line interface (CLI). It uses `argparse` to handle command-line arguments and orchestrates the application's behavior based on user input.
*   **`~oolboxv2/__init__.py`:** The package initializer. It imports and exposes the core classes and functions, making them accessible to other parts of the application.

## Key Components

*   **`App`:** The main application class, responsible for loading modules, managing the application state, and executing functions.
*   **`MainTool`:** A base class for creating new tools.
*   **`FileHandler`:** A utility for file system operations.
*   **`Style`:** A utility for styling console output.
*   **`Spinner`:** A utility for displaying a spinner in the console.
*   **`get_logger`, `setup_logging`:** Functions for setting up and using the application's logger.
