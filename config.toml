[server]
ip = "0.0.0.0"
port = 8080
dist_path = "./dist"
open_modules = ["CloudM.AuthManager",  "ChatModule","IdeaEnhancer", "CloudM","agent_ui", "registry", "CloudM.email_services", "UltimateTTT", "Canvas", "SimpleCore", "InnovationGraphManager"]
init_modules = ["CloudM", "DealScraper",  "ChatModule", "isaa", "IdeaEnhancer", "registry","AffiliateLinkGenerator", "FileWidget", "MarkdownPublisher", "CounterTracker", "Canvas", "SimpleCore", "InnovationGraphManager"]
watch_modules = ["CloudM", "DealScraper",  "ChatModule", "isaa", "AffiliateLinkGenerator","registry", "Canvas", "FileWidget", "MarkdownPublisher", "CounterTracker", "SimpleCore", "InnovationGraphManager"]


[toolbox]
client_prifix="collective-debugApi-client"
timeout_seconds = 60
max_instances = 2

[session]
secret_key = "F95AF20084A99457B6C0FDD43AD3E4607654A7EA26E3CA90F578CF52408D076D"
duration_minutes = 3600


[env]
PYO3_PYTHON={value = "C:\\Users\\<USER>\\Workspace\\ToolBoxV2\\python_env\\python.exe" , relative = false, force = true }
